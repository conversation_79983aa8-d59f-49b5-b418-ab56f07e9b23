//
// Copyright 2005-2007 Adobe Systems Incorporated
//
// Distributed under the Boost Software License, Version 1.0
// See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt
//
#ifndef BOOST_GIL_CONCEPTS_DETAIL_UTILITY_HPP
#define BOOST_GIL_CONCEPTS_DETAIL_UTILITY_HPP

namespace boost { namespace gil { namespace detail {

// TODO: Remove, replace with ignore_unised?
template <typename T>
void initialize_it(T&) {}

}}} // namespace boost::gil::detail

#endif
