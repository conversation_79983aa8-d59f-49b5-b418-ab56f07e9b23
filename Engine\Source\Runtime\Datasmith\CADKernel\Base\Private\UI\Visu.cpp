// Copyright Epic Games, Inc. All Rights Reserved.

#include "UI/Visu.h"

namespace UE::CADKernel
{
const TCHAR* VisuPropertyNames[] =
{
	TEXT("Bounding Box"),
	TEXT("Points"),
	TEXT("Elements"),
	TEXT("Curves"),
	TEXT("Iso"),
	TEXT("Control Lines"),
	TEXT("Edges"),
	TEXT("Nodes"),
	TEXT("Border Edges"),
	TEXT("Multiple Edges"),
	TEXT("Geometrical Borders"),
	TEXT("Control Points"),

	TEXT("YellowPoint"),
	TEXT("YellowCurve"),
	TEXT("BluePoint"),
	TEXT("BlueCurve"),
	TEXT("RedPoint"),
	TEXT("RedCurve"),
	TEXT("PurplePoint"),
	TEXT("PurpleCurve"),
	TEXT("GreenPoint"),
	TEXT("GreenCurve"),
	TEXT("PinkPoint"),
	TEXT("PinkCurve"),
	TEXT("OrangePoint"),
	TEXT("OrangeCurve"),
	nullptr
};


}
