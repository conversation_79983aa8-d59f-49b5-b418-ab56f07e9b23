// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Blueprints/TextureShareBlueprintContainersBase.h"

#ifdef TEXTURESHARE_TextureShareBlueprintContainersBase_generated_h
#error "TextureShareBlueprintContainersBase.generated.h already included, missing '#pragma once' in TextureShareBlueprintContainersBase.h"
#endif
#define TEXTURESHARE_TextureShareBlueprintContainersBase_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FTextureShareSendTextureDesc **************************************
#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainersBase_h_15_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTextureShareSendTextureDesc_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTextureShareSendTextureDesc;
// ********** End ScriptStruct FTextureShareSendTextureDesc ****************************************

// ********** Begin ScriptStruct FTextureShareReceiveTextureDesc ***********************************
#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainersBase_h_33_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTextureShareReceiveTextureDesc_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTextureShareReceiveTextureDesc;
// ********** End ScriptStruct FTextureShareReceiveTextureDesc *************************************

// ********** Begin ScriptStruct FTextureShareTexturesDesc *****************************************
#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainersBase_h_51_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTextureShareTexturesDesc_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTextureShareTexturesDesc;
// ********** End ScriptStruct FTextureShareTexturesDesc *******************************************

// ********** Begin ScriptStruct FTextureShareCustomData *******************************************
#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainersBase_h_69_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTextureShareCustomData_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTextureShareCustomData;
// ********** End ScriptStruct FTextureShareCustomData *********************************************

// ********** Begin ScriptStruct FTextureShareObjectSyncSettings ***********************************
#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainersBase_h_87_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTextureShareObjectSyncSettings_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTextureShareObjectSyncSettings;
// ********** End ScriptStruct FTextureShareObjectSyncSettings *************************************

// ********** Begin ScriptStruct FTextureShareObjectDesc *******************************************
#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainersBase_h_105_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTextureShareObjectDesc_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTextureShareObjectDesc;
// ********** End ScriptStruct FTextureShareObjectDesc *********************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainersBase_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
