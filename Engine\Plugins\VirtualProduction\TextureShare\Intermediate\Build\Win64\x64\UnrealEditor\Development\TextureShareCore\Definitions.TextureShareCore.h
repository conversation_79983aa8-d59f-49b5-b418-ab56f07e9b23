// Generated by UnrealB<PERSON>Tool (UEBuildModuleCPP.cs) : Shared PCH Definitions for TextureShareCore
#pragma once
#include "SharedDefinitions.Core.Cpp20.h"
#undef TEXTURESHARECORE_API
#define UE_IS_ENGINE_MODULE 1
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 0
#define UE_VALIDATE_EXPERIMENTAL_API 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define TEXTURESHARECORE_DEBUGLOG 0
#define TEXTURESHARECORE_BARRIER_DEBUGLOG 0
#define TEXTURESHARECORE_VULKAN 0
#define TEXTURESHARECORE_SDK 0
#define UE_MODULE_NAME "TextureShareCore"
#define UE_PLUGIN_NAME "TextureShare"
#define TEXTURESHARECORE_API DLLEXPORT
#define D3D11RHI_API DLLIMPORT
#define WITH_MGPU 1
#define RHI_WANT_RESOURCE_INFO 1
#define RHI_API DLLIMPORT
#define D3D12RHI_PLATFORM_HAS_CUSTOM_INTERFACE 0
#define PROFILE 1
#define D3D12RHI_API DLLIMPORT
#define WITH_PIX_EVENT_RUNTIME 1
