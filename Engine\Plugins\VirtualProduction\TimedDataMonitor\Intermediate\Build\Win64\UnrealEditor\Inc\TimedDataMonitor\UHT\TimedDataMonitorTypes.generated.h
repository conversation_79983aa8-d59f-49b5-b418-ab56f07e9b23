// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "TimedDataMonitorTypes.h"

#ifdef TIMEDDATAMONITOR_TimedDataMonitorTypes_generated_h
#error "TimedDataMonitorTypes.generated.h already included, missing '#pragma once' in TimedDataMonitorTypes.h"
#endif
#define TIMEDDATAMONITOR_TimedDataMonitorTypes_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FTimedDataMonitorInputIdentifier **********************************
#define FID_Engine_Plugins_VirtualProduction_TimedDataMonitor_Source_TimedDataMonitor_Public_TimedDataMonitorTypes_h_14_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTimedDataMonitorInputIdentifier_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTimedDataMonitorInputIdentifier;
// ********** End ScriptStruct FTimedDataMonitorInputIdentifier ************************************

// ********** Begin ScriptStruct FTimedDataMonitorChannelIdentifier ********************************
#define FID_Engine_Plugins_VirtualProduction_TimedDataMonitor_Source_TimedDataMonitor_Public_TimedDataMonitorTypes_h_36_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTimedDataMonitorChannelIdentifier_Statics; \
	static class UScriptStruct* StaticStruct();


struct FTimedDataMonitorChannelIdentifier;
// ********** End ScriptStruct FTimedDataMonitorChannelIdentifier **********************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Engine_Plugins_VirtualProduction_TimedDataMonitor_Source_TimedDataMonitor_Public_TimedDataMonitorTypes_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
