// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Blueprints/TextureShareBlueprintContainers.h"

#ifdef TEXTURESHARE_TextureShareBlueprintContainers_generated_h
#error "TextureShareBlueprintContainers.generated.h already included, missing '#pragma once' in TextureShareBlueprintContainers.h"
#endif
#define TEXTURESHARE_TextureShareBlueprintContainers_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

 
class UTextureShareObject;

// ********** Begin Class UTextureShareObject ******************************************************
#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_15_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSendCustomData);


TEXTURESHARE_API UClass* Z_Construct_UClass_UTextureShareObject_NoRegister();

#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_15_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUTextureShareObject(); \
	friend struct Z_Construct_UClass_UTextureShareObject_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend TEXTURESHARE_API UClass* Z_Construct_UClass_UTextureShareObject_NoRegister(); \
public: \
	DECLARE_CLASS2(UTextureShareObject, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/TextureShare"), Z_Construct_UClass_UTextureShareObject_NoRegister) \
	DECLARE_SERIALIZER(UTextureShareObject)


#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_15_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UTextureShareObject(UTextureShareObject&&) = delete; \
	UTextureShareObject(const UTextureShareObject&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UTextureShareObject); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UTextureShareObject); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UTextureShareObject)


#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_11_PROLOG
#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_15_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_15_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_15_INCLASS_NO_PURE_DECLS \
	FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_15_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UTextureShareObject;

// ********** End Class UTextureShareObject ********************************************************

// ********** Begin Class UTextureShare ************************************************************
#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_51_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetTextureShareObjects); \
	DECLARE_FUNCTION(execRemoveTextureShareObject); \
	DECLARE_FUNCTION(execGetOrCreateTextureShareObject);


TEXTURESHARE_API UClass* Z_Construct_UClass_UTextureShare_NoRegister();

#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_51_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUTextureShare(); \
	friend struct Z_Construct_UClass_UTextureShare_Statics; \
	static UClass* GetPrivateStaticClass(); \
	friend TEXTURESHARE_API UClass* Z_Construct_UClass_UTextureShare_NoRegister(); \
public: \
	DECLARE_CLASS2(UTextureShare, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/TextureShare"), Z_Construct_UClass_UTextureShare_NoRegister) \
	DECLARE_SERIALIZER(UTextureShare)


#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_51_ENHANCED_CONSTRUCTORS \
	/** Deleted move- and copy-constructors, should never be used */ \
	UTextureShare(UTextureShare&&) = delete; \
	UTextureShare(const UTextureShare&) = delete; \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UTextureShare); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UTextureShare); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UTextureShare)


#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_47_PROLOG
#define FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_51_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_51_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_51_INCLASS_NO_PURE_DECLS \
	FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h_51_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


class UTextureShare;

// ********** End Class UTextureShare **************************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Engine_Plugins_VirtualProduction_TextureShare_Source_TextureShare_Public_Blueprints_TextureShareBlueprintContainers_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
