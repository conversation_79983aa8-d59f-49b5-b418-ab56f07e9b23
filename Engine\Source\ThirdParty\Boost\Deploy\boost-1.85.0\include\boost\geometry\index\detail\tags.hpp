// Boost.Geometry Index
//
// Tags used by the predicates checks implementation.
//
// Copyright (c) 2011-2013 <PERSON>, Lodz, Poland.
//
// Use, modification and distribution is subject to the Boost Software License,
// Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_GEOMETRY_INDEX_DETAIL_TAGS_HPP
#define BOOST_GEOMETRY_INDEX_DETAIL_TAGS_HPP

namespace boost { namespace geometry { namespace index {

namespace detail {

struct value_tag {};
struct bounds_tag {};

} // namespace detail

}}} // namespace boost::geometry::index

#endif // BOOST_GEOMETRY_INDEX_RTREE_TAGS_HPP
