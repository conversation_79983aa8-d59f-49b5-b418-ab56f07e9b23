/*
 *  Copyright 2019 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef LOGGING_RTC_EVENT_LOG_LOGGED_EVENTS_H_
#define LOGGING_RTC_EVENT_LOG_LOGGED_EVENTS_H_

// TODO(terelius): Delete this forwarding header when downstream
// projects have been updated.
#include "logging/rtc_event_log/events/logged_rtp_rtcp.h"

#endif  // LOGGING_RTC_EVENT_LOG_LOGGED_EVENTS_H_
