// Boost.Geometry (aka GGL, Generic Geometry Library)

// Copyright (c) 2012 Barend Gehrels, Amsterdam, the Netherlands.
// Copyright (c) 2012 <PERSON>, Paris, France.
// Copyright (c) 2012 <PERSON><PERSON><PERSON>, London, UK.

// This file was modified by Oracle on 2014-2020.
// Modifications copyright (c) 2014-2020 Oracle and/or its affiliates.
// Contributed and/or modified by <PERSON>, on behalf of Oracle

// Use, modification and distribution is subject to the Boost Software License,
// Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_GEOMETRY_UTIL_BARE_TYPE_HPP
#define BOOST_GEOMETRY_UTIL_BARE_TYPE_HPP


#include <boost/config/header_deprecated.hpp>
BOOST_HEADER_DEPRECATED("<boost/geometry/util/type_traits.hpp>")

#include <boost/geometry/util/type_traits.hpp>


#endif // BOOST_GEOMETRY_UTIL_BARE_TYPE_HPP
