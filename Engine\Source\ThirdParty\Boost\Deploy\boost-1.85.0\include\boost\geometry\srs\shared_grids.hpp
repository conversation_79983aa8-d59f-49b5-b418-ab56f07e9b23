// Boost.Geometry

// Copyright (c) 2018-2019, Oracle and/or its affiliates.
// Contributed and/or modified by <PERSON>, on behalf of Oracle

// Use, modification and distribution is subject to the Boost Software License,
// Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_GEOMETRY_SRS_SHARED_GRIDS_HPP
#define BOOST_GEOMETRY_SRS_SHARED_GRIDS_HPP


#include <boost/geometry/srs/shared_grids_boost.hpp>


namespace boost { namespace geometry
{

namespace srs
{


typedef shared_grids_boost shared_grids;


} // namespace srs


}} // namespace boost::geometry


#endif // BOOST_GEOMETRY_SRS_SHARED_GRIDS_HPP
