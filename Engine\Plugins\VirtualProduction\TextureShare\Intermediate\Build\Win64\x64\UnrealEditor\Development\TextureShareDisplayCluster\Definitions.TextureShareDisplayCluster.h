// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for TextureShareDisplayCluster
#pragma once
#include "SharedDefinitions.UnrealEd.Cpp20.h"
#undef TEXTURESHAREDISPLAYCLUSTER_API
#define UE_IS_ENGINE_MODULE 1
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 0
#define UE_VALIDATE_EXPERIMENTAL_API 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define TEXTURESHAREDISPLAYCLUSTER_DEBUGLOG 0
#define UE_MODULE_NAME "TextureShareDisplayCluster"
#define UE_PLUGIN_NAME "TextureShare"
#define TEXTURESHARE_DEBUGLOG 0
#define TEXTURESHARE_VULKAN 0
#define TEXTURESHARE_API DLLIMPORT
#define TEXTURESHARECORE_DEBUGLOG 0
#define TEXTURESHARECORE_BARRIER_DEBUGLOG 0
#define TEXTURESHARECORE_VULKAN 0
#define TEXTURESHARECORE_SDK 0
#define TEXTURESHARECORE_API DLLIMPORT
#define D3D11RHI_API DLLIMPORT
#define D3D12RHI_PLATFORM_HAS_CUSTOM_INTERFACE 0
#define PROFILE 1
#define D3D12RHI_API DLLIMPORT
#define WITH_PIX_EVENT_RUNTIME 1
#define TEXTURESHAREDISPLAYCLUSTER_API DLLEXPORT
#define DISPLAYCLUSTER_API DLLIMPORT
#define CINEMATICCAMERA_API DLLIMPORT
#define DISPLAYCLUSTERCONFIGURATION_API DLLIMPORT
#define ACTORLAYERUTILITIES_API DLLIMPORT
#define MEDIAASSETS_API DLLIMPORT
#define MEDIA_API DLLIMPORT
#define MEDIAUTILS_API DLLIMPORT
#define WITH_MEDIA_IO_AUDIO_DEBUGGING 0
#define MEDIAIOCORE_API DLLIMPORT
#define IMAGEWRITEQUEUE_API DLLIMPORT
#define OPENCOLORIO_API DLLIMPORT
#define WITH_OCIO 1
#define OPENCOLORIOWRAPPER_API DLLIMPORT
#define DISPLAYCLUSTERPROJECTION_API DLLIMPORT
#define DISPLAYCLUSTERSHADERS_API DLLIMPORT
#define DISPLAYCLUSTERWARP_API DLLIMPORT
#define DISPLAYCLUSTERLIGHTCARDEDITORSHADERS_API DLLIMPORT
#define DISPLAYCLUSTERLIGHTCARDEXTENDER_API DLLIMPORT
#define ENHANCEDINPUT_API DLLIMPORT
#define VIRTUALPRODUCTION_API DLLIMPORT
#define DISPLAYCLUSTERCONFIGURATOR_API DLLIMPORT
