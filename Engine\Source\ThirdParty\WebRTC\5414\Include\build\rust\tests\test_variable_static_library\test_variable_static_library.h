// Copyright 2021 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef BUILD_RUST_TESTS_TEST_VARIABLE_STATIC_LIBRARY_TEST_VARIABLE_STATIC_LIBRARY_H_
#define BUILD_RUST_TESTS_TEST_VARIABLE_STATIC_LIBRARY_TEST_VARIABLE_STATIC_LIBRARY_H_

#include <string>

void do_something_in_sandbox_or_memory_safe_language(const std::string& input);

#endif  // BUILD_RUST_TESTS_TEST_VARIABLE_STATIC_LIBRARY_TEST_VARIABLE_STATIC_LIBRARY_H_
