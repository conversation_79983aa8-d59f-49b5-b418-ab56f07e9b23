{"configurations": [{"name": "MCPGameProjectEditor Editor Win64 Development (MCPGameProject)", "compilerPath": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "cStandard": "c17", "cppStandard": "c++20", "intelliSenseMode": "msvc-x64", "compileCommands": ["C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_MCPGameProject.json"]}, {"name": "Win32", "compilerPath": "C:\\Program Files (x86)\\Microsoft Visual Studio\\2022\\BuildTools\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe", "cStandard": "c17", "cppStandard": "c++20", "intelliSenseMode": "msvc-x64", "compileCommands": ["C:\\Program Files\\Epic Games\\UE_5.6\\.vscode\\compileCommands_Default.json"]}], "version": 4}