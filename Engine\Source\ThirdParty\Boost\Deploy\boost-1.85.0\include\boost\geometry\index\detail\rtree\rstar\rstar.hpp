// Boost.Geometry Index
//
// R-tree R*-tree algorithm implementation
//
// Copyright (c) 2011-2013 <PERSON>, Lodz, Poland.
//
// Use, modification and distribution is subject to the Boost Software License,
// Version 1.0. (See accompanying file LICENSE_1_0.txt or copy at
// http://www.boost.org/LICENSE_1_0.txt)

#ifndef BOOST_GEOMETRY_INDEX_DETAIL_RTREE_RSTAR_RSTAR_HPP
#define BOOST_GEOMETRY_INDEX_DETAIL_RTREE_RSTAR_RSTAR_HPP

#include <boost/geometry/index/detail/rtree/rstar/insert.hpp>
#include <boost/geometry/index/detail/rtree/rstar/choose_next_node.hpp>
#include <boost/geometry/index/detail/rtree/rstar/redistribute_elements.hpp>

#endif // BOOST_GEOMETRY_INDEX_DETAIL_RTREE_RSTAR_RSTAR_HPP
