// Generated by UnrealB<PERSON>Tool (UEBuildModuleCPP.cs) : Shared PCH Definitions for TimedDataMonitor
#pragma once
#include "SharedDefinitions.UnrealEd.Cpp20.h"
#undef TIMEDDATAMONITOR_API
#define UE_IS_ENGINE_MODULE 1
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 0
#define UE_VALIDATE_EXPERIMENTAL_API 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_MODULE_NAME "TimedDataMonitor"
#define UE_PLUGIN_NAME "TimedDataMonitor"
#define TIMEDDATAMONITOR_API DLLEXPORT
#define STAGEDATACORE_API DLLIMPORT
